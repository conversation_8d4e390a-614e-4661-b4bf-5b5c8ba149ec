# 浮点数输入功能升级说明

## 概述
本次升级将电机控制系统的位置和速度输入从整数类型改为浮点数类型，支持更精确的控制。

## 修改的文件

### 1. SiemensModbus.py
**新增功能：**
- 添加了 `write_float_register()` 方法，用于将浮点数写入PLC寄存器
- 修改了 `set_motor_position()` 和 `set_motor_v()` 方法，使用浮点数写入

**技术细节：**
- 使用 `struct.pack('>f', float_value)` 将浮点数转换为IEEE 754格式
- 将32位浮点数分解为两个16位寄存器值
- 使用大端字节序（Big Endian）确保与PLC兼容

### 2. motor_control_ui.py
**已有功能确认：**
- `motor1_control()` 和 `motor2_control()` 方法已经支持浮点数输入
- 包含完整的错误处理和用户友好的错误提示

**界面改进：**
- 更新了输入框的占位符文本，明确说明支持小数输入
- 位置输入框：`设置位置 (支持小数，如: 123.45)`
- 速度输入框：`设置速度 (支持小数，如: 10.5)`

## 功能特性

### 支持的输入格式
- 正浮点数：`123.45`, `10.5`, `0.001`
- 负浮点数：`-10.25`, `-0.5`
- 整数：`100`, `0` (自动转换为浮点数)
- 空值：自动设置为 `0.0`

### 错误处理
- 无效输入会显示友好的错误对话框
- 控制台输出详细的错误信息用于调试
- 系统异常会显示错误对话框并记录日志

### 精度说明
- 使用IEEE 754单精度浮点数格式
- 精度约为6-7位有效数字
- 对于电机控制应用完全满足精度要求

## 使用示例

### 有效输入示例
```
位置输入：123.45    -> 电机移动到位置 123.45
速度输入：10.5      -> 电机以速度 10.5 运行
位置输入：-50.25    -> 电机移动到位置 -50.25
速度输入：0.1       -> 电机以低速 0.1 运行
```

### 错误输入处理
```
位置输入：abc       -> 显示错误对话框："参数格式错误"
速度输入：12.34.56  -> 显示错误对话框："参数格式错误"
空输入              -> 自动设置为 0.0
```

## 测试验证
- 创建了 `test_float_input.py` 测试脚本
- 验证了浮点数转换功能
- 验证了寄存器格式转换
- 所有测试通过，功能正常

## 兼容性
- 向后兼容：整数输入仍然有效
- PLC通信：使用标准IEEE 754格式，与大多数PLC系统兼容
- 用户界面：保持原有操作方式，只是增加了浮点数支持

## 注意事项
1. 确保PLC端配置支持浮点数寄存器
2. 浮点数精度受IEEE 754标准限制
3. 建议在实际使用前进行充分测试
4. 如遇到精度问题，可考虑使用定点数或缩放因子

## 升级完成
系统现在完全支持浮点数位置和速度输入，提供更精确的电机控制能力。
