# 电机控制程序用户指南

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install pymodbus PyQt5
```

### 2. 启动程序
```bash
python motor_control_ui.py
```

### 3. 正确操作流程
1. **连接PLC** → 点击"连接 PLC"按钮
2. **确认连接** → 按钮变绿色显示"断开 PLC"
3. **使能电机** → 点击"电机使能"按钮
4. **输入参数** → 在位置和速度框中输入数值
5. **开始控制** → 点击"开始旋转"按钮

## 📝 输入格式说明

### ✅ 支持的输入格式
- **整数**: `100`, `0`, `-50`
- **小数**: `123.45`, `-10.25`, `0.001`
- **科学计数法**: `1e3` (1000), `1.23e-2` (0.0123)
- **空值**: 留空自动设为 `0`
- **中文句号**: `123。45` 自动转换为 `123.45`

### ❌ 不支持的格式
- **多个小数点**: `12.34.56`
- **字母混合**: `abc123`, `12a34`
- **包含空格**: `12 34`
- **错误符号**: `++123`, `--123`, `123-`
- **英文逗号**: `12,34` (作为小数点)

### 📊 数值范围
- **最小值**: `-999,999`
- **最大值**: `999,999`
- **精度**: 支持6-7位有效数字

## 🔧 故障排除

### 问题1: "值不是有效数字"
**原因**: 输入格式不正确
**解决方案**:
- 检查是否包含字母或特殊字符
- 确保只有一个小数点
- 使用英文小数点 `.` 而不是逗号 `,`
- 参考上面的支持格式

**示例**:
```
❌ 错误: 12.34.56, abc123, 12 34
✅ 正确: 123.45, -10.25, 0.001
```

### 问题2: "请先连接PLC"
**原因**: PLC未连接
**解决方案**:
1. 点击"连接 PLC"按钮
2. 确认按钮变为绿色"断开 PLC"
3. 检查PLC IP地址和网络连接

### 问题3: 电机没有反应
**可能原因**:
1. PLC未连接 → 先连接PLC
2. 电机未使能 → 点击"电机使能"
3. 参数错误 → 检查输入格式
4. PLC通信故障 → 检查网络和PLC状态

### 问题4: 连接PLC失败
**检查项目**:
- PLC IP地址是否正确 (默认: ***********)
- 网络连接是否正常
- PLC是否开机并运行
- 防火墙是否阻止连接

## 🎯 使用技巧

### 1. 精确控制
- 使用小数进行精确定位: `123.456`
- 低速运行使用小数速度: `0.5`, `0.1`

### 2. 快速输入
- 空值自动为0，无需输入`0`
- 支持科学计数法: `1e3` = `1000`

### 3. 错误预防
- 输入后程序会自动验证格式
- 错误时会显示详细提示信息
- 可以根据提示修正输入

### 4. 状态监控
- 实时显示电机位置和速度
- 连接状态通过按钮颜色显示
- 控制台输出详细日志信息

## 📋 常用操作示例

### 基本定位
```
位置: 100      速度: 10     → 移动到位置100，速度10
位置: -50.5    速度: 5.2    → 移动到位置-50.5，速度5.2
位置: 0        速度: 1      → 回到原点，低速1
```

### 精确控制
```
位置: 123.456  速度: 0.5    → 精确定位，慢速移动
位置: 0.001    速度: 0.1    → 微调位置
```

### 快速移动
```
位置: 1000     速度: 100    → 快速移动到远距离
位置: 1e3      速度: 1e2    → 使用科学计数法
```

## ⚠️ 安全注意事项

1. **首次使用**: 建议使用小的位置和速度值进行测试
2. **紧急停止**: 如需紧急停止，可断开PLC连接
3. **参数检查**: 发送指令前仔细检查位置和速度参数
4. **监控状态**: 密切关注实时状态显示
5. **网络稳定**: 确保PLC网络连接稳定

## 📞 技术支持

如果遇到问题：
1. 查看控制台输出的详细错误信息
2. 参考本指南的故障排除部分
3. 检查PLC和网络连接状态
4. 验证输入参数格式

## 🔄 版本更新

**当前版本特性**:
- ✅ 浮点数位置和速度控制
- ✅ 智能输入验证和错误提示
- ✅ PLC连接状态管理
- ✅ 实时状态监控
- ✅ 离线模式支持
- ✅ 详细的用户反馈

程序已经解决了"值不是有效数字"和"电机旋转没反应"的问题，现在可以稳定可靠地进行电机控制！
