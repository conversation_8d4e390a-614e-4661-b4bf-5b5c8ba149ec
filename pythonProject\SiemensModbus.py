import struct
import time

from pymodbus.client import ModbusTcpClient


class PLC_comm:
    """
    PLC通讯类，通过 Modbus TCP 与PLC通信，实现伺服电机控制。
    支持多个电机，每个电机对应一组寄存器。

    默认配置：
        第1个电机的寄存器：[0, 2, 4]
            - 寄存器0：目标位置
            - 寄存器2：启动/停止控制（1=启动，0=停止）
            - 寄存器4：转动速度或频率
    """

    def __init__(self, ip, port=502, slave=1, motor_registers=None):
        """
        初始化PLC通讯类

        参数：
            ip: PLC的IP地址
            port: PLC的端口号（Modbus TCP默认502）
            slave: 从机ID（PLC设备地址）
            motor_registers: 字典，键为电机ID，值为对应的寄存器地址列表。
                             例如：{1: [0, 2, 4], 2: [10, 12, 14]}
        """
        self.ip = ip
        self.port = port
        self.slave = slave
        # 如果未提供电机寄存器配置，默认第1个电机使用 [0,2,4]
        self.motor_registers = motor_registers if motor_registers is not None else {1: [0, 2, 4,6]}

    def connect(self):
        try:
            self.client = ModbusTcpClient(self.ip, port=self.port)
            connection_result = self.client.connect()
            if connection_result:
                print(f"PLC连接成功: {self.ip}:{self.port}")
                return True
            else:
                print(f"PLC连接失败: {self.ip}:{self.port}")
                return False
        except Exception as e:
            print(f"PLC连接异常: {e}")
            return False

    def is_connected(self):
        """检查PLC连接状态"""
        try:
            return self.client and self.client.connected
        except:
            return False


    def write_register(self, address, value):
        """
        写入单个保持寄存器

        参数：
            address: 寄存器地址（相对于保持寄存器起始地址）
            value: 要写入的数值
        """
        if not self.is_connected():
            print(f"PLC未连接，无法写入寄存器 {address}")
            return False

        try:
            result = self.client.write_register(address, value, slave=self.slave)
            if result.isError():
                print(f"写入寄存器 {address} 失败: {result}")
                return False
            print(f"成功写入寄存器 {address}: {value}")
            return True
        except Exception as e:
            print(f"写入寄存器 {address} 异常: {e}")
            return False

    def write_float_register(self, address, float_value):
        """
        将浮点数写入两个连续的保持寄存器

        参数：
            address: 起始寄存器地址
            float_value: 要写入的浮点数值
        """
        if not self.is_connected():
            print(f"PLC未连接，无法写入浮点寄存器 {address}")
            return False

        try:
            # 将浮点数转换为两个16位寄存器值
            raw = struct.pack('>f', float_value)  # 大端格式的浮点数
            high_word, low_word = struct.unpack('>HH', raw)  # 解包为两个16位整数

            # 写入两个连续寄存器
            result = self.client.write_registers(address, [high_word, low_word], slave=self.slave)
            if result.isError():
                print(f"写入浮点寄存器 {address} 失败: {result}")
                return False
            print(f"成功写入浮点寄存器 {address}: {float_value} -> [{high_word}, {low_word}]")
            return True
        except Exception as e:
            print(f"写入浮点寄存器 {address} 异常: {e}")
            return False

    def read_register(self, address, count=1):
        """
        读取保持寄存器数据

        参数：
            address: 起始寄存器地址
            count: 要读取的寄存器数量
        返回：
            寄存器数值列表
        """
        if not self.is_connected():
            print(f"PLC未连接，无法读取寄存器 {address}")
            return None

        try:
            result = self.client.read_holding_registers(address, count=count, slave=self.slave)
            if result.isError():
                print(f"读取寄存器 {address} 失败: {result}")
                return None
            return result.registers
        except Exception as e:
            print(f"读取寄存器 {address} 异常: {e}")
            return None

    def motor_power(self, motor_id):
        regs = self.motor_registers[motor_id]
        # 控制启动/停止（写入寄存器列表中的第二个寄存器）
        self.write_register(regs[0], 1)

    def set_motor_position(self,motor_id,p):
        regs = self.motor_registers[motor_id]
        # 将浮点数位置写入寄存器（使用两个连续寄存器存储浮点数）
        self.write_float_register(regs[2], float(p))

    def set_motor_v(self,motor_id,v):
        regs = self.motor_registers[motor_id]
        # 将浮点数速度写入寄存器（使用两个连续寄存器存储浮点数）
        self.write_float_register(regs[3], float(v))

    def set_motor(self,motor_id):
        regs = self.motor_registers[motor_id]
        # 控制启动/停止（写入寄存器列表中的第二个寄存器）
        self.write_register(regs[1], 1)

    def motor_control(self, motor_id, position, speed):

        self.set_motor_position(motor_id,position)
        self.set_motor_v(motor_id,speed)
        self.set_motor(motor_id)

    def read_motor1_position(self):

        position = self.read_register(address=8, count=2)
        # 假设寄存器顺序是高位在前（Big Endian）
        raw = struct.pack('>HH', position[0], position[1])  # > 表示大端，HH 表示两个16位整数
        return struct.unpack('>f', raw)[0]  # >f 表示大端 float

    def read_motor1_v(self):

        v = self.read_register(address=10, count=2)
        # 假设寄存器顺序是高位在前（Big Endian）
        raw = struct.pack('>HH', v[0], v[1])  # > 表示大端，HH 表示两个16位整数
        return struct.unpack('>f', raw)[0]  # >f 表示大端 float

    def read_motor2_position(self):

        position = self.read_register(address=12, count=2)
        # 假设寄存器顺序是高位在前（Big Endian）
        raw = struct.pack('>HH', position[0], position[1])  # > 表示大端，HH 表示两个16位整数
        return struct.unpack('>f', raw)[0]  # >f 表示大端 float

    def read_motor2_v(self):

        v = self.read_register(address=14, count=2)
        # 假设寄存器顺序是高位在前（Big Endian）
        raw = struct.pack('>HH', v[0], v[1])  # > 表示大端，HH 表示两个16位整数
        return struct.unpack('>f', raw)[0]  # >f 表示大端 float


    def close(self):
        """关闭与PLC的连接"""
        self.client.close()


if __name__ == "__main__":
    # 示例配置
    PLC_IP = '***********'
    PLC_PORT = 502
    SLAVE_ID = 1
    # 默认配置：第1个电机使用寄存器 [0,2,4]，第2个电机示例使用寄存器 [10,12,14]
    motor_registers = {
        1: [1, 2, 3, 4],
        2: [5, 6, 7, 8]
    }

    # 创建PLC通讯实例
    plc = PLC_comm(ip=PLC_IP, port=PLC_PORT, slave=SLAVE_ID, motor_registers=motor_registers)
    plc.connect()
    # plc.write_register(0,1)
    # plc.write_register(2, 180)
    # plc.write_register(3, 10)
    # time.sleep(5)
    # plc.write_register(1, 1)
    # result = plc.read_register(address=10, count=2)
    # # 假设寄存器顺序是高位在前（Big Endian）
    # raw = struct.pack('>HH', result[0], result[1])  # > 表示大端，HH 表示两个16位整数
    # float_value = struct.unpack('>f', raw)[0]  # >f 表示大端 float
    try:
        float_value=plc.read_motor1_position()
        print(type(float_value))
        print(float_value)
        float_value=plc.read_motor1_v()
        print(type(float_value))
        print(float_value)
        float_value=plc.read_motor2_position()
        print(type(float_value))
        print(float_value)
        float_value=plc.read_motor2_v()
        print(type(float_value))
        print(float_value)
    except Exception:
        print(2)

    # try:
    #     # 控制第1个电机：启动、设置目标位置8192、转速2048
    #     plc.motor_power(motor_id=2)
    # finally:
    #     plc.close()
