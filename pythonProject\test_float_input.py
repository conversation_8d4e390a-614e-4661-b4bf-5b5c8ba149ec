#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试浮点数输入功能的脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_float_conversion():
    """测试浮点数转换功能"""
    print("=== 测试浮点数转换功能 ===")
    
    test_cases = [
        "123.45",
        "0.5",
        "-10.25",
        "100",
        "0",
        "",
        "  ",
        "abc",
        "12.34.56"
    ]
    
    for test_input in test_cases:
        try:
            if not test_input.strip():
                result = 0.0
                print(f"输入: '{test_input}' -> 结果: {result} (空值默认)")
            else:
                result = float(test_input)
                print(f"输入: '{test_input}' -> 结果: {result} ✓")
        except ValueError as e:
            print(f"输入: '{test_input}' -> 错误: {e} ✗")

def test_struct_conversion():
    """测试浮点数与寄存器转换"""
    print("\n=== 测试浮点数与寄存器转换 ===")
    import struct
    
    test_floats = [123.45, -10.25, 0.0, 1000.123, 0.001]
    
    for float_val in test_floats:
        try:
            # 转换为寄存器格式
            raw = struct.pack('>f', float_val)
            high_word, low_word = struct.unpack('>HH', raw)
            
            # 从寄存器格式转换回浮点数
            raw_back = struct.pack('>HH', high_word, low_word)
            float_back = struct.unpack('>f', raw_back)[0]
            
            print(f"原始值: {float_val} -> 寄存器: [{high_word}, {low_word}] -> 恢复值: {float_back}")
            
            # 检查精度
            if abs(float_val - float_back) < 1e-6:
                print("  转换精度: ✓")
            else:
                print(f"  转换精度: ✗ (差值: {abs(float_val - float_back)})")
                
        except Exception as e:
            print(f"转换错误: {e}")

if __name__ == "__main__":
    test_float_conversion()
    test_struct_conversion()
    
    print("\n=== 测试完成 ===")
    print("如果所有测试都通过，说明浮点数输入功能已正确实现。")
