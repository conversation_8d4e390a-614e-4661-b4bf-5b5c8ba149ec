#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试输入验证功能的脚本
"""

import sys
import os
import re

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def validate_and_convert_input(input_str, field_name):
    """
    验证并转换输入字符串为浮点数（与UI中的方法相同）
    """
    if input_str is None:
        return 0.0
        
    # 去除首尾空格
    cleaned_input = str(input_str).strip()
    
    # 空输入默认为0
    if not cleaned_input:
        return 0.0
    
    # 替换中文符号为英文符号
    cleaned_input = cleaned_input.replace('，', ',').replace('。', '.')
    
    # 检查是否包含非法字符
    if not re.match(r'^[-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?$', cleaned_input):
        raise ValueError(f"{field_name}格式无效: '{input_str}' - 只能包含数字、小数点和负号")
    
    try:
        value = float(cleaned_input)
        
        # 检查数值范围（根据实际需求调整）
        if abs(value) > 999999:
            raise ValueError(f"{field_name}数值过大: {value} - 请输入合理范围内的数值")
            
        return value
        
    except (ValueError, OverflowError) as e:
        if "could not convert" in str(e):
            raise ValueError(f"{field_name}无法转换为数字: '{input_str}' - 请检查输入格式")
        else:
            raise ValueError(f"{field_name}数值错误: {str(e)}")

def test_input_validation():
    """测试各种输入情况"""
    print("=== 测试输入验证功能 ===")
    
    # 测试用例：[输入值, 期望结果, 是否应该成功]
    test_cases = [
        # 正常情况
        ("123.45", 123.45, True),
        ("0", 0.0, True),
        ("0.0", 0.0, True),
        ("-10.25", -10.25, True),
        ("100", 100.0, True),
        ("0.001", 0.001, True),
        ("1000.123", 1000.123, True),
        
        # 空值情况
        ("", 0.0, True),
        ("  ", 0.0, True),
        (None, 0.0, True),
        
        # 中文符号
        ("123。45", 123.45, True),
        ("123，45", None, False),  # 逗号不是小数点
        
        # 科学计数法
        ("1e3", 1000.0, True),
        ("1.23e-2", 0.0123, True),
        
        # 错误情况
        ("abc", None, False),
        ("12.34.56", None, False),
        ("12a34", None, False),
        ("++123", None, False),
        ("--123", None, False),
        ("123-", None, False),
        ("12 34", None, False),
        ("12.34.56.78", None, False),
        
        # 边界情况
        ("999999", 999999.0, True),
        ("1000000", None, False),  # 超出范围
        ("-999999", -999999.0, True),
        ("-1000000", None, False),  # 超出范围
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (input_val, expected, should_succeed) in enumerate(test_cases, 1):
        try:
            result = validate_and_convert_input(input_val, "测试字段")
            
            if should_succeed:
                if abs(result - expected) < 1e-10:  # 浮点数比较
                    print(f"✓ 测试 {i:2d}: '{input_val}' -> {result} (正确)")
                    success_count += 1
                else:
                    print(f"✗ 测试 {i:2d}: '{input_val}' -> {result}, 期望: {expected} (数值不匹配)")
            else:
                print(f"✗ 测试 {i:2d}: '{input_val}' -> {result} (应该失败但成功了)")
                
        except ValueError as e:
            if not should_succeed:
                print(f"✓ 测试 {i:2d}: '{input_val}' -> 错误: {e} (正确拒绝)")
                success_count += 1
            else:
                print(f"✗ 测试 {i:2d}: '{input_val}' -> 错误: {e} (不应该失败)")
        except Exception as e:
            print(f"✗ 测试 {i:2d}: '{input_val}' -> 未知错误: {e}")
    
    print(f"\n测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！输入验证功能正常工作。")
    else:
        print("⚠️  部分测试失败，需要检查验证逻辑。")

def test_common_user_errors():
    """测试常见用户输入错误"""
    print("\n=== 测试常见用户输入错误 ===")
    
    common_errors = [
        "12.34.56",      # 多个小数点
        "abc123",        # 字母数字混合
        "123abc",        # 数字字母混合
        "12 34",         # 包含空格
        "12,34",         # 使用逗号作为小数点（英文环境）
        "12。34",        # 使用中文句号
        "++123",         # 多个符号
        "--123",         # 多个符号
        "123-",          # 符号位置错误
        "*******",       # 多个小数点
        "",              # 空字符串（应该成功，默认为0）
        "   ",           # 空白字符（应该成功，默认为0）
    ]
    
    for error_input in common_errors:
        try:
            result = validate_and_convert_input(error_input, "位置")
            if error_input.strip() == "":
                print(f"✓ '{error_input}' -> {result} (空值正确处理)")
            else:
                print(f"⚠️  '{error_input}' -> {result} (可能需要检查)")
        except ValueError as e:
            print(f"✓ '{error_input}' -> 错误: {str(e)[:50]}... (正确拒绝)")

if __name__ == "__main__":
    test_input_validation()
    test_common_user_errors()
    
    print("\n=== 使用建议 ===")
    print("1. 支持的格式：123.45, -10.25, 0, 0.001, 1e3")
    print("2. 空输入自动设为0")
    print("3. 自动处理中文句号（。）")
    print("4. 数值范围：-999999 到 999999")
    print("5. 详细错误提示帮助用户纠正输入")
