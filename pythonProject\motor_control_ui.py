import sys

from PyQt5.QtCore import QTimer
from PyQt5.QtWidgets import (
    QApplication, QWidget, QPushButton, QVBoxLayout,
    QHBoxLayout, QLabel, QLineEdit, QGroupBox, QFormLayout, QMessageBox
)
import SiemensModbus


class MotorControl(QWidget):
    def __init__(self, motor_name):
        super().__init__()
        self.motor_name = motor_name
        self.init_ui()


    def init_ui(self):
        layout = QVBoxLayout()

        self.enable_but = QPushButton(f'{self.motor_name} 使能')
        self.position_input = QLineEdit()
        self.position_input.setPlaceholderText('设置位置 (支持小数，如: 123.45)')
        self.speed_input = QLineEdit()
        self.speed_input.setPlaceholderText('设置速度 (支持小数，如: 10.5)')
        self.start_but = QPushButton(f'{self.motor_name} 开始旋转')

        layout.addWidget(self.enable_but)
        layout.addWidget(QLabel('位置:'))
        layout.addWidget(self.position_input)
        layout.addWidget(QLabel('速度:'))
        layout.addWidget(self.speed_input)
        layout.addWidget(self.start_but)

        self.setLayout(layout)

class StatusMonitor(QWidget):
    def __init__(self, motor_name):
        super().__init__()
        group = QGroupBox(f'{motor_name} 状态')
        form = QFormLayout()
        # 用 QLabel 显示，调用 setText 刷新
        self.pos_lbl   = QLabel("0.00")
        self.speed_lbl = QLabel("0.00")
        form.addRow('位置:', self.pos_lbl)
        form.addRow('速度:', self.speed_lbl)
        group.setLayout(form)

        layout = QVBoxLayout()
        layout.addWidget(group)
        self.setLayout(layout)

    def update(self, pos, speed):
        self.pos_lbl.setText(f"{pos:.2f}")
        self.speed_lbl.setText(f"{speed:.2f}")

class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('双电机控制界面')
        self.init_ui()

        # 初始化PLC连接
        motor_registers = {
            1: [0, 1, 2, 3],
            2: [4, 5, 6, 7]
        }
        self.plc = SiemensModbus.PLC_comm(ip='***********', port=502, slave=1, motor_registers=motor_registers)
        self.plc_connected = False

        # 状态监控定时器
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_status)
        self.timer.start(500)
        self.last_pos1,self.last_pos2,self.last_speed1,self.last_speed2=0,0,0,0

    def init_ui(self):
        main_layout = QVBoxLayout()

        # PLC连接按钮
        self.plc_but = QPushButton('连接 PLC')
        self.plc_but.clicked.connect(self.toggle_plc_connection)
        main_layout.addWidget(self.plc_but)

        # 电机1控制
        motor1_group = QGroupBox('电机1 控制')
        motor1_control = MotorControl('电机1')
        motor1_layout = QVBoxLayout()
        motor1_layout.addWidget(motor1_control)
        motor1_group.setLayout(motor1_layout)
        motor1_control.enable_but.clicked.connect(self.motor1_enable)
        motor1_control.start_but.clicked.connect(
            lambda: self.motor1_control(motor1_control.position_input.text(), motor1_control.speed_input.text()))

        # 电机2控制
        motor2_group = QGroupBox('电机2 控制')
        motor2_control = MotorControl('电机2')
        motor2_layout = QVBoxLayout()
        motor2_layout.addWidget(motor2_control)
        motor2_group.setLayout(motor2_layout)
        motor2_control.enable_but.clicked.connect(self.motor2_enable)
        motor2_control.start_but.clicked.connect(
            lambda: self.motor2_control(motor2_control.position_input.text(), motor2_control.speed_input.text()))

        # 添加到主布局
        motors_layout = QHBoxLayout()
        motors_layout.addWidget(motor1_group)
        motors_layout.addWidget(motor2_group)

        main_layout.addLayout(motors_layout)
        self.setLayout(main_layout)

        stat_box = QGroupBox('状态监控')
        h_stat = QHBoxLayout()
        self.stat1 = StatusMonitor('转台轴')
        self.stat2 = StatusMonitor('步进轴')
        h_stat.addWidget(self.stat1)
        h_stat.addWidget(self.stat2)
        stat_box.setLayout(h_stat)
        main_layout.addWidget(stat_box)





    def toggle_plc_connection(self):
        """切换PLC连接状态"""
        if self.plc_connected:
            # 断开连接
            try:
                self.plc.close()
                self.plc_connected = False
                self.plc_but.setText('连接 PLC')
                self.plc_but.setStyleSheet("")
                print("PLC连接已断开")
            except Exception as e:
                print(f"断开PLC连接时发生错误: {e}")
        else:
            # 尝试连接
            try:
                if self.plc.connect():
                    self.plc_connected = True
                    self.plc_but.setText('断开 PLC')
                    self.plc_but.setStyleSheet("background-color: lightgreen;")
                    print("PLC连接成功")
                    QMessageBox.information(self, "连接成功", "PLC连接成功！")
                else:
                    self.plc_connected = False
                    print("PLC连接失败")
                    QMessageBox.warning(self, "连接失败", "无法连接到PLC，请检查网络和PLC设置。")
            except Exception as e:
                self.plc_connected = False
                error_msg = f"PLC连接异常: {str(e)}"
                print(error_msg)
                QMessageBox.critical(self, "连接错误", error_msg)

    def motor1_enable(self):
        if not self.plc_connected:
            QMessageBox.warning(self, "连接错误", "请先连接PLC！")
            return
        self.plc.motor_power(1)

    def motor2_enable(self):
        if not self.plc_connected:
            QMessageBox.warning(self, "连接错误", "请先连接PLC！")
            return
        self.plc.motor_power(2)
    def motor1_control(self,position,v):
        if not self.plc_connected:
            QMessageBox.warning(self, "连接错误", "请先连接PLC！")
            return

        try:
            # 支持浮点数输入，并进行输入验证
            if not position.strip():
                position = 0.0
            else:
                position = float(position)

            if not v.strip():
                v = 0.0
            else:
                v = float(v)

            self.plc.motor_control(1, position, v)
            print(f"电机1控制成功: 位置={position}, 速度={v}")
            QMessageBox.information(self, "控制成功", f"电机1控制指令已发送\n位置: {position}\n速度: {v}")
        except ValueError as e:
            error_msg = f"电机1输入参数格式错误！\n位置: '{position}'\n速度: '{v}'\n请输入有效的数字。"
            QMessageBox.warning(self, "参数错误", error_msg)
            print(f"电机1控制参数错误: 位置='{position}', 速度='{v}', 错误: {e}")
        except Exception as e:
            error_msg = f"电机1控制发生错误: {str(e)}"
            QMessageBox.critical(self, "控制错误", error_msg)
            print(f"电机1控制发生未知错误: {e}")

    def motor2_control(self,position,v):
        if not self.plc_connected:
            QMessageBox.warning(self, "连接错误", "请先连接PLC！")
            return

        try:
            # 支持浮点数输入，并进行输入验证
            if not position.strip():
                position = 0.0
            else:
                position = float(position)

            if not v.strip():
                v = 0.0
            else:
                v = float(v)

            self.plc.motor_control(2, position, v)
            print(f"电机2控制成功: 位置={position}, 速度={v}")
            QMessageBox.information(self, "控制成功", f"电机2控制指令已发送\n位置: {position}\n速度: {v}")
        except ValueError as e:
            error_msg = f"电机2输入参数格式错误！\n位置: '{position}'\n速度: '{v}'\n请输入有效的数字。"
            QMessageBox.warning(self, "参数错误", error_msg)
            print(f"电机2控制参数错误: 位置='{position}', 速度='{v}', 错误: {e}")
        except Exception as e:
            error_msg = f"电机2控制发生错误: {str(e)}"
            QMessageBox.critical(self, "控制错误", error_msg)
            print(f"电机2控制发生未知错误: {e}")

    def update_status(self):
        # 从 PLC 读取实际位置和速度（仅在连接时）
        if not self.plc_connected:
            # PLC未连接时显示默认值
            self.stat1.update(0.0, 0.0)
            self.stat2.update(0.0, 0.0)
            return

        try:
            pos1 = self.plc.read_motor1_position()
            if pos1 is not None and abs(pos1) <= 10000:
                self.last_pos1 = pos1
            else:
                pos1 = self.last_pos1

            speed1 = self.plc.read_motor1_v()
            if speed1 is not None and abs(speed1) <= 10000:
                self.last_speed1 = speed1
            else:
                speed1 = self.last_speed1

            pos2 = self.plc.read_motor2_position()
            if pos2 is not None and abs(pos2) <= 10000:
                self.last_pos2 = pos2
            else:
                pos2 = self.last_pos2

            speed2 = self.plc.read_motor2_v()
            if speed2 is not None and abs(speed2) <= 10000:
                self.last_speed2 = speed2
            else:
                speed2 = self.last_speed2

            self.stat1.update(pos1, speed1)
            self.stat2.update(pos2, speed2)

        except Exception as e:
            print(f"状态更新异常: {e}")
            # 发生异常时使用上次的值
            self.stat1.update(self.last_pos1, self.last_speed1)
            self.stat2.update(self.last_pos2, self.last_speed2)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
